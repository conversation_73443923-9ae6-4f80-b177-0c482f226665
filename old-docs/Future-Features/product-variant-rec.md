### Recommendations for the `ProductVariant` Model

Product variants represent the specific versions of a product that a customer can buy. The following fields are critical for accurate inventory, pricing, and fulfillment.

| Field Name | Type | Purpose & Importance |
| :--- | :--- | :--- |
| **`cost_price`** | `DecimalField` | **(High Importance)** The price at which you acquired the product variant. This is absolutely essential for calculating profit margins and making informed pricing decisions. |
| **`barcode`** | `CharField` | **(High Importance)** For storing universal identifiers like UPC, EAN, or ISBN. It's critical for inventory management with barcode scanners, integrating with POS systems, and listing on marketplaces like Amazon or Google Shopping. |
| **`dimensions (length, width, height)`** | `DecimalField` (x3) | **(High Importance)** Along with `weight` (which you already have), dimensions are often required by shipping carriers to calculate shipping costs accurately, especially for dimensional weight pricing. |
| **`low_stock_threshold`** | `PositiveIntegerField` | **(Medium Importance)** Allows you to set a threshold that triggers a notification (e.g., email to a manager) when the `stock_qty` for a variant falls below this number. This helps prevent stockouts. |
| **`is_default`** | `BooleanField` | **(Medium Importance)** A simple way to designate one variant as the default to be displayed when a customer first lands on the product page. You would need to add logic to ensure only one variant per product can be the default. |
| **`tax_class`** | `ForeignKey` | **(Medium Importance)** If you sell products that have different tax rates (e.g., standard vs. reduced tax items), a foreign key to a `TaxClass` model would allow for more flexible and accurate tax calculations. |
