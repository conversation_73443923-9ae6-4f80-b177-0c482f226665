# Recommended Fields for ProductVariant Model

| Field | Purpose | Importance | Reason Missing |
|-------|---------|------------|---------------|
| **Cost Price** | Tracks acquisition/production cost for profit margins. | High | No field for cost price tracking. |
| **Barcode/UPC** | Unique identifier for retail scanning and integration. | Medium | No barcode field, despite `sku` presence. |
| **Minimum Order Quantity (MOQ)** | Specifies minimum purchase quantity. | Medium | No field to enforce minimum orders. |
| **Backorder Status** | Indicates if variant can be sold when out of stock. | Medium | No field for backorder policies. |
| **Restock Date** | Indicates restocking schedule for out-of-stock variants. | Low to Medium | No field for restocking schedules. |
| **Variant-Specific Images** | Unique images per variant (e.g., different colors). | Medium | `ProductImage` linked but no explicit variant-specific image assignment. |
| **Customs Information (HS Code)** | HS code for international shipping and customs compliance. | Medium | No field for customs-related data. |