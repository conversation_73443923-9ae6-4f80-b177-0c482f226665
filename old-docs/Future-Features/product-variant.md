# ProductVariant Model - Consolidated Enhancement Recommendations

## Overview
This document consolidates all unique field suggestions for enhancing the `ProductVariant` model from various recommendation files. Product variants represent the specific versions of a product that customers can purchase.

## Current Model Analysis
The existing `ProductVariant` model includes: `price`, `price_label`, `sku`, `stock_qty`, `is_active`, `weight`, `condition`, `product`, `attribute_value`, `order`, `created_at`, `updated_at`.

## Recommended Fields by Priority

### High Priority Fields

| Field Name | Data Type | Purpose/Description | Importance |
|------------|-----------|-------------------|------------|
| **`cost_price`** | `DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)` | The price at which you acquired the product variant. Essential for calculating profit margins and pricing decisions. | ⭐⭐⭐⭐⭐ |
| **`compare_at_price`** | `DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)` | Original/recommended price for discount display. Shows MSRP for marketing purposes. | ⭐⭐⭐⭐⭐ |
| **`barcode`** | `CharField(max_length=50, blank=True)` | Universal identifier for retail scanning and POS integration. Critical for inventory management. | ⭐⭐⭐⭐⭐ |
| **`upc`** | `CharField(max_length=12, blank=True)` | Universal Product Code for marketplace listings and inventory tracking. | ⭐⭐⭐⭐⭐ |
| **`low_stock_threshold`** | `PositiveIntegerField(default=5)` | Triggers notifications when stock falls below this number. Helps prevent stockouts. | ⭐⭐⭐⭐⭐ |
| **`availability_status`** | `CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='in_stock')` | More detailed stock status beyond just quantity. Improves customer experience. | ⭐⭐⭐⭐⭐ |
| **`track_inventory`** | `BooleanField(default=True)` | Controls whether inventory is tracked for this variant. Essential for inventory management. | ⭐⭐⭐⭐⭐ |
| **`allow_backorders`** | `BooleanField(default=False)` | Indicates if variant can be sold when out of stock. Important for order management. | ⭐⭐⭐⭐⭐ |

### Medium Priority Fields

| Field Name | Data Type | Purpose/Description | Importance |
|------------|-----------|-------------------|------------|
| **`variant_length`** | `DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)` | Variant-specific length when different from product dimensions. Important for shipping calculations. | ⭐⭐⭐⭐ |
| **`variant_width`** | `DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)` | Variant-specific width when different from product dimensions. Important for shipping calculations. | ⭐⭐⭐⭐ |
| **`variant_height`** | `DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)` | Variant-specific height when different from product dimensions. Important for shipping calculations. | ⭐⭐⭐⭐ |
| **`is_default`** | `BooleanField(default=False)` | Designates one variant as default to display when customer first lands on product page. | ⭐⭐⭐⭐ |
| **`requires_shipping`** | `BooleanField(default=True)` | Handles digital products and items that don't require shipping. | ⭐⭐⭐⭐ |
| **`separate_shipping_cost`** | `DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)` | Custom shipping cost for specific variants that require special handling. | ⭐⭐⭐ |
| **`shipping_class`** | `CharField(max_length=50, blank=True)` | Shipping classification for variants with special shipping requirements. | ⭐⭐⭐ |
| **`min_order_quantity`** | `PositiveIntegerField(default=1)` | Specifies minimum purchase quantity for this variant. | ⭐⭐⭐ |
| **`restock_date`** | `DateField(null=True, blank=True)` | Indicates restocking schedule for out-of-stock variants. | ⭐⭐⭐ |
| **`vendor`** | `ForeignKey('Vendor', on_delete=models.SET_NULL, null=True, blank=True)` | Multi-vendor support and supply chain management. | ⭐⭐⭐ |
| **`vendor_sku`** | `CharField(max_length=100, blank=True)` | Vendor's SKU for this variant. Important for dropshipping and procurement. | ⭐⭐⭐ |
| **`lead_time_days`** | `PositiveIntegerField(null=True, blank=True)` | Expected lead time for restocking or procurement. | ⭐⭐⭐ |

### Low Priority Fields

| Field Name | Data Type | Purpose/Description | Importance |
|------------|-----------|-------------------|------------|
| **`tax_class`** | `ForeignKey('TaxClass', on_delete=models.SET_NULL, null=True, blank=True)` | Variant-specific tax classification for complex tax scenarios. | ⭐⭐ |
| **`taxable`** | `BooleanField(default=True)` | Whether this variant is subject to tax. | ⭐⭐ |
| **`preorder_available`** | `BooleanField(default=False)` | Indicates if this variant is available for pre-order. | ⭐⭐ |
| **`preorder_release_date`** | `DateField(null=True, blank=True)` | Expected release date for pre-order variants. | ⭐⭐ |
| **`max_preorder_quantity`** | `PositiveIntegerField(null=True, blank=True)` | Maximum quantity available for pre-order. | ⭐⭐ |
| **`age_restricted`** | `BooleanField(default=False)` | Indicates if variant requires age verification. | ⭐⭐ |
| **`minimum_age`** | `PositiveIntegerField(null=True, blank=True)` | Minimum age required to purchase this variant. | ⭐⭐ |
| **`warranty_period_months`** | `PositiveIntegerField(null=True, blank=True)` | Warranty period in months for this variant. | ⭐⭐ |
| **`warranty_description`** | `TextField(blank=True)` | Detailed warranty information for customer reference. | ⭐⭐ |
| **`hs_code`** | `CharField(max_length=20, blank=True)` | Harmonized System code for international shipping and customs compliance. | ⭐⭐ |

## Availability Status Choices
```python
AVAILABILITY_CHOICES = (
    ('in_stock', 'In Stock'),
    ('out_of_stock', 'Out of Stock'),
    ('preorder', 'Pre-order'),
    ('discontinued', 'Discontinued'),
    ('backordered', 'Backordered'),
    ('low_stock', 'Low Stock'),
)
```

## Implementation Recommendations

### Phase 1 (Immediate - High Priority)
1. Add inventory management fields (`low_stock_threshold`, `track_inventory`, `allow_backorders`)
2. Implement cost and pricing fields (`cost_price`, `compare_at_price`)
3. Add product identification (`barcode`, `upc`)
4. Implement `availability_status` with choices

### Phase 2 (Short-term - Medium Priority)
1. Add variant-specific dimensions
2. Implement shipping information fields
3. Add vendor/supplier information
4. Include `is_default` functionality
5. Add minimum order quantity

### Phase 3 (Long-term - Low Priority)
1. Add tax information fields
2. Implement pre-order functionality
3. Add age restrictions and warranty information
4. Include customs information (HS codes)

## Database Considerations

### Indexes to Add
```python
class Meta:
    indexes = [
        models.Index(fields=['availability_status']),
        models.Index(fields=['barcode']),
        models.Index(fields=['upc']),
        models.Index(fields=['is_default']),
        models.Index(fields=['low_stock_threshold']),
    ]
```

### Validation Methods
```python
def clean(self):
    if self.compare_at_price and self.compare_at_price < self.price:
        raise ValidationError('Compare at price must be greater than or equal to price')
    
    if self.preorder_available and not self.preorder_release_date:
        raise ValidationError('Pre-order release date is required for pre-order products')
    
    if self.is_default:
        # Ensure only one default variant per product
        existing_default = ProductVariant.objects.filter(
            product=self.product, is_default=True
        ).exclude(pk=self.pk)
        if existing_default.exists():
            raise ValidationError('Only one variant can be set as default per product')
```

## Related Models to Create

### Vendor Model
```python
class Vendor(models.Model):
    name = models.CharField(max_length=100)
    contact_email = models.EmailField()
    is_active = models.BooleanField(default=True)
```

### InventoryLog Model
```python
class InventoryLog(models.Model):
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='inventory_logs')
    change_type = models.CharField(max_length=20)  # 'sale', 'restock', 'adjustment', 'return'
    quantity_change = models.IntegerField()
    quantity_before = models.PositiveIntegerField()
    quantity_after = models.PositiveIntegerField()
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
    user = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True)
```

### PriceHistory Model
```python
class PriceHistory(models.Model):
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='price_history')
    price = models.DecimalField(max_digits=10, decimal_places=2)
    compare_at_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    effective_date = models.DateTimeField()
    created_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True)
```

## Business Logic Integration

### Inventory Management
- Auto-update `availability_status` based on `stock_qty` and business rules
- Send alerts when stock falls below `low_stock_threshold`
- Handle backorder logic when `allow_backorders` is True
- Log all inventory changes in InventoryLog model

### Pricing Logic
- Validate `compare_at_price` >= `price`
- Calculate discount percentage for display: `((compare_at_price - price) / compare_at_price) * 100`
- Track price changes in PriceHistory model
- Calculate profit margins using `cost_price`

### Shipping Integration
- Use `requires_shipping` to filter shippable items
- Calculate shipping based on variant dimensions and weight
- Apply `shipping_class` rules for special handling
- Use `separate_shipping_cost` when specified

## Admin Interface Enhancements
- Add fieldsets for logical grouping (Pricing, Inventory, Shipping, etc.)
- Include list filters for availability_status, is_default, requires_shipping
- Add search fields for barcode, UPC, vendor_sku
- Implement bulk actions for inventory updates
- Add inline editing for inventory logs and price history
