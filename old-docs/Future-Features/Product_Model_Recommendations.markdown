# Recommended Fields for Product Model

| Field | Purpose | Importance | Reason Missing |
|-------|---------|------------|---------------|
| **SKU** | Unique identifier for inventory tracking and order processing. | High | Only `ProductVariant` has `sku`; base `Product` needs one for non-variant products. |
| **Price** | Base price for products without variants or as a reference. | Medium | Only `ProductVariant` has `price`; non-variant products lack pricing. |
| **Stock Quantity** | Tracks inventory for products without variants. | Medium | Only `ProductVariant` has `stock_qty`; base products need inventory tracking. |
| **Tax Class** | Defines tax rate/category (e.g., standard, exempt). | High | No field for tax rule association. |
| **Shipping Class** | Specifies shipping requirements (e.g., standard, fragile). | Medium | No field for shipping characteristics. |
| **Meta Title** | SEO title for search engine visibility. | Medium | No SEO-specific fields. |
| **Meta Description** | SEO description for better search result click-through. | Medium | No SEO-specific fields. |
| **Tags/Keywords** | Enhances searchability and filtering (e.g., "organic"). | Medium | No field for flexible tagging. |
| **Dimensions** | Length, width, height for shipping and storage. | Medium | Only `weight` in `ProductVariant`; dimensions absent in both models. |